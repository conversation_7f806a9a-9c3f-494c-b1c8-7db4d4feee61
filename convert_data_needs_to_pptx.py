#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
将多模态育幼数据需求分析HTML转换为PowerPoint演示文稿
"""

from pptx import Presentation
from pptx.util import Inches, Pt
from pptx.enum.text import PP_ALIGN
from pptx.dml.color import RGBColor
from pptx.enum.shapes import MSO_SHAPE
import re

def create_data_needs_presentation():
    """创建多模态育幼数据需求分析PPT"""
    
    # 创建演示文稿
    prs = Presentation()
    
    # 设置幻灯片尺寸为16:9
    prs.slide_width = Inches(13.33)
    prs.slide_height = Inches(7.5)
    
    # 定义颜色
    title_color = RGBColor(44, 62, 80)  # 深蓝色
    subtitle_color = RGBColor(41, 128, 185)  # 蓝色
    text_color = RGBColor(52, 73, 94)  # 深灰色
    highlight_color = RGBColor(52, 152, 219)  # 亮蓝色
    urgent_color = RGBColor(231, 76, 60)  # 红色
    success_color = RGBColor(39, 174, 96)  # 绿色
    
    # 第1页：封面页
    slide1 = prs.slides.add_slide(prs.slide_layouts[6])  # 空白布局
    
    # 标题
    title_box = slide1.shapes.add_textbox(Inches(1), Inches(1), Inches(11.33), Inches(1.5))
    title_frame = title_box.text_frame
    title_frame.text = "多模态育幼数据需求分析"
    title_para = title_frame.paragraphs[0]
    title_para.font.size = Pt(44)
    title_para.font.bold = True
    title_para.font.color.rgb = title_color
    title_para.alignment = PP_ALIGN.CENTER
    
    # 副标题
    subtitle_box = slide1.shapes.add_textbox(Inches(1), Inches(2.8), Inches(11.33), Inches(1))
    subtitle_frame = subtitle_box.text_frame
    subtitle_frame.text = "基于现有10个功能模块的数据缺口分析"
    subtitle_para = subtitle_frame.paragraphs[0]
    subtitle_para.font.size = Pt(24)
    subtitle_para.font.color.rgb = subtitle_color
    subtitle_para.alignment = PP_ALIGN.CENTER
    
    # 关键信息
    info_box = slide1.shapes.add_textbox(Inches(1), Inches(4), Inches(11.33), Inches(1))
    info_frame = info_box.text_frame
    info_frame.text = "医院合作 · 数据补充 · 临床验证"
    info_para = info_frame.paragraphs[0]
    info_para.font.size = Pt(20)
    info_para.font.color.rgb = text_color
    info_para.alignment = PP_ALIGN.CENTER
    
    # 统计数据
    stats_data = [
        ("13,000+", "现有数据条数"),
        ("10", "功能模块"),
        ("50MB+", "数据总量")
    ]
    
    for i, (number, label) in enumerate(stats_data):
        x_pos = Inches(2 + i * 3.5)
        # 数字
        num_box = slide1.shapes.add_textbox(x_pos, Inches(5.2), Inches(2.5), Inches(0.8))
        num_frame = num_box.text_frame
        num_frame.text = number
        num_para = num_frame.paragraphs[0]
        num_para.font.size = Pt(36)
        num_para.font.bold = True
        num_para.font.color.rgb = highlight_color
        num_para.alignment = PP_ALIGN.CENTER
        
        # 标签
        label_box = slide1.shapes.add_textbox(x_pos, Inches(6), Inches(2.5), Inches(0.5))
        label_frame = label_box.text_frame
        label_frame.text = label
        label_para = label_frame.paragraphs[0]
        label_para.font.size = Pt(14)
        label_para.font.color.rgb = text_color
        label_para.alignment = PP_ALIGN.CENTER
    
    # 第2页：现有数据集概览
    slide2 = prs.slides.add_slide(prs.slide_layouts[6])
    
    # 标题
    title_box = slide2.shapes.add_textbox(Inches(0.5), Inches(0.3), Inches(12.33), Inches(0.8))
    title_frame = title_box.text_frame
    title_frame.text = "📊 现有数据集完成情况"
    title_para = title_frame.paragraphs[0]
    title_para.font.size = Pt(32)
    title_para.font.bold = True
    title_para.font.color.rgb = title_color
    
    # 已完成模块
    modules_data = [
        ("🗣️ 语音构音监测", "1,219条", "基于52词汇标准化测试"),
        ("🏃 家庭场景运动监测", "1,499条", "CribHD多模态数据集"),
        ("🍎 饮食多样性监测", "571条", "基于国家卫健委指南"),
        ("📏 体格生长监测", "400条", "多指标综合分析"),
        ("🎯 发音纠正指导", "650+条", "五步纠正法"),
        ("🤸 粗大运动发展", "4,586条", "0-36个月里程碑"),
        ("✋ 精细动作发展", "827条", "四维度结构化分析"),
        ("😊 社会情绪发展", "4,041条", "PEC理论+ASQ:SE-2"),
        ("💬 语言发展指导", "707条", "基于专业教材"),
        ("🥄 膳食营养指导", "已整合", "整合到饮食监测中")
    ]
    
    # 创建模块网格
    for i, (name, count, desc) in enumerate(modules_data):
        row = i // 2
        col = i % 2
        x_pos = Inches(0.5 + col * 6)
        y_pos = Inches(1.5 + row * 1.1)
        
        # 模块框
        module_box = slide2.shapes.add_textbox(x_pos, y_pos, Inches(5.5), Inches(1))
        module_frame = module_box.text_frame
        module_frame.margin_left = Inches(0.1)
        module_frame.margin_top = Inches(0.05)
        
        # 模块名称
        p1 = module_frame.paragraphs[0]
        p1.text = f"{name} - {count}"
        p1.font.size = Pt(14)
        p1.font.bold = True
        p1.font.color.rgb = text_color
        
        # 描述
        p2 = module_frame.add_paragraph()
        p2.text = desc
        p2.font.size = Pt(11)
        p2.font.color.rgb = RGBColor(127, 140, 141)
    
    # 第3页：关键数据缺口分析
    slide3 = prs.slides.add_slide(prs.slide_layouts[6])
    
    # 标题
    title_box = slide3.shapes.add_textbox(Inches(0.5), Inches(0.3), Inches(12.33), Inches(0.8))
    title_frame = title_box.text_frame
    title_frame.text = "🔍 关键数据缺口分析"
    title_para = title_frame.paragraphs[0]
    title_para.font.size = Pt(32)
    title_para.font.bold = True
    title_para.font.color.rgb = title_color
    
    # 急需补充的数据类型
    urgent_box = slide3.shapes.add_textbox(Inches(0.5), Inches(1.2), Inches(12), Inches(2))
    urgent_frame = urgent_box.text_frame
    urgent_frame.margin_left = Inches(0.2)
    
    p1 = urgent_frame.paragraphs[0]
    p1.text = "🚨 急需补充的数据类型"
    p1.font.size = Pt(20)
    p1.font.bold = True
    p1.font.color.rgb = urgent_color
    
    urgent_items = [
        "真实临床数据：目前主要依赖AI生成，缺乏真实病例",
        "异常发育案例：正常发育数据充足，异常案例稀缺", 
        "多模态融合验证：各模块独立，缺乏跨模态关联",
        "长期追踪数据：缺乏同一婴儿的发育轨迹数据"
    ]
    
    for item in urgent_items:
        p = urgent_frame.add_paragraph()
        p.text = f"• {item}"
        p.font.size = Pt(14)
        p.font.color.rgb = text_color
        p.level = 1
    
    # 需要改进的数据质量
    improve_box = slide3.shapes.add_textbox(Inches(0.5), Inches(3.5), Inches(12), Inches(2))
    improve_frame = improve_box.text_frame
    improve_frame.margin_left = Inches(0.2)
    
    p1 = improve_frame.paragraphs[0]
    p1.text = "⚠️ 需要改进的数据质量"
    p1.font.size = Pt(20)
    p1.font.bold = True
    p1.font.color.rgb = RGBColor(243, 156, 18)
    
    improve_items = [
        "专家标注验证：AI生成数据需要医学专家审核",
        "数据平衡性：某些年龄段、性别数据分布不均",
        "地域代表性：主要基于中文环境，缺乏多地域验证",
        "实时性验证：静态数据较多，缺乏动态监测验证"
    ]
    
    for item in improve_items:
        p = improve_frame.add_paragraph()
        p.text = f"• {item}"
        p.font.size = Pt(14)
        p.font.color.rgb = text_color
        p.level = 1
    
    # 第4页：医院合作需求
    slide4 = prs.slides.add_slide(prs.slide_layouts[6])
    
    # 标题
    title_box = slide4.shapes.add_textbox(Inches(0.5), Inches(0.3), Inches(12.33), Inches(0.8))
    title_frame = title_box.text_frame
    title_frame.text = "🏥 医院合作数据需求"
    title_para = title_frame.paragraphs[0]
    title_para.font.size = Pt(32)
    title_para.font.bold = True
    title_para.font.color.rgb = title_color
    
    # 合作需求四个方面
    cooperation_data = [
        ("🎯 临床数据采集", [
            "真实患儿发育评估记录",
            "医生诊断和治疗方案", 
            "家长反馈和观察记录",
            "治疗效果追踪数据"
        ]),
        ("👨‍⚕️ 专家标注服务", [
            "儿科医生专业标注",
            "发育评估专家审核",
            "语言治疗师验证", 
            "营养师专业指导"
        ]),
        ("🔬 异常案例收集", [
            "发育迟缓案例",
            "语言障碍案例",
            "运动发育异常",
            "营养相关疾病"
        ]),
        ("📈 验证测试支持", [
            "模型准确性验证",
            "临床适用性测试",
            "用户体验评估",
            "安全性评估"
        ])
    ]
    
    for i, (title, items) in enumerate(cooperation_data):
        row = i // 2
        col = i % 2
        x_pos = Inches(0.5 + col * 6)
        y_pos = Inches(1.3 + row * 2.8)
        
        # 标题框
        title_box = slide4.shapes.add_textbox(x_pos, y_pos, Inches(5.5), Inches(0.5))
        title_frame = title_box.text_frame
        title_para = title_frame.paragraphs[0]
        title_para.text = title
        title_para.font.size = Pt(16)
        title_para.font.bold = True
        title_para.font.color.rgb = highlight_color
        
        # 内容框
        content_box = slide4.shapes.add_textbox(x_pos, y_pos + Inches(0.5), Inches(5.5), Inches(2))
        content_frame = content_box.text_frame
        content_frame.margin_left = Inches(0.1)
        
        for item in items:
            p = content_frame.add_paragraph() if content_frame.paragraphs[0].text else content_frame.paragraphs[0]
            p.text = f"• {item}"
            p.font.size = Pt(12)
            p.font.color.rgb = text_color
    
    # 第5页：具体数据需求清单
    slide5 = prs.slides.add_slide(prs.slide_layouts[6])

    # 标题
    title_box = slide5.shapes.add_textbox(Inches(0.5), Inches(0.3), Inches(12.33), Inches(0.8))
    title_frame = title_box.text_frame
    title_frame.text = "📋 具体数据需求清单"
    title_para = title_frame.paragraphs[0]
    title_para.font.size = Pt(32)
    title_para.font.bold = True
    title_para.font.color.rgb = title_color

    # 高优先级数据需求
    high_priority_box = slide5.shapes.add_textbox(Inches(0.5), Inches(1.2), Inches(12), Inches(2.5))
    high_priority_frame = high_priority_box.text_frame
    high_priority_frame.margin_left = Inches(0.2)

    p1 = high_priority_frame.paragraphs[0]
    p1.text = "🔥 高优先级数据需求"
    p1.font.size = Pt(20)
    p1.font.bold = True
    p1.font.color.rgb = urgent_color

    high_priority_items = [
        "异常发育案例：发育迟缓100-200例，语言障碍50-100例",
        "专家标注数据：现有AI数据审核1000条，新增标注500条",
        "诊断标准对照：200条，治疗方案验证：100条"
    ]

    for item in high_priority_items:
        p = high_priority_frame.add_paragraph()
        p.text = f"• {item}"
        p.font.size = Pt(14)
        p.font.color.rgb = text_color
        p.level = 1

    # 中等优先级数据需求
    medium_priority_box = slide5.shapes.add_textbox(Inches(0.5), Inches(4), Inches(12), Inches(2.5))
    medium_priority_frame = medium_priority_box.text_frame
    medium_priority_frame.margin_left = Inches(0.2)

    p1 = medium_priority_frame.paragraphs[0]
    p1.text = "⭐ 中等优先级数据需求"
    p1.font.size = Pt(20)
    p1.font.bold = True
    p1.font.color.rgb = RGBColor(243, 156, 18)

    medium_priority_items = [
        "长期追踪数据：同一婴儿6个月追踪50例，治疗效果追踪30例",
        "多模态融合数据：视频+音频+文本100例，生理指标+行为观察50例",
        "环境因素+发育评估：80例，家长反馈+专业评估：100例"
    ]

    for item in medium_priority_items:
        p = medium_priority_frame.add_paragraph()
        p.text = f"• {item}"
        p.font.size = Pt(14)
        p.font.color.rgb = text_color
        p.level = 1

    # 第6页：合作方案建议
    slide6 = prs.slides.add_slide(prs.slide_layouts[6])

    # 标题
    title_box = slide6.shapes.add_textbox(Inches(0.5), Inches(0.3), Inches(12.33), Inches(0.8))
    title_frame = title_box.text_frame
    title_frame.text = "🤝 合作方案建议"
    title_para = title_frame.paragraphs[0]
    title_para.font.size = Pt(32)
    title_para.font.bold = True
    title_para.font.color.rgb = title_color

    # 四个阶段
    phases_data = [
        ("📋 阶段一：数据收集框架建立", "1个月", [
            "制定数据收集协议",
            "建立伦理审查流程",
            "培训数据收集人员",
            "搭建数据管理平台"
        ]),
        ("🔬 阶段二：重点数据采集", "3个月", [
            "异常发育案例收集",
            "专家标注和审核",
            "多模态数据采集",
            "质量控制和验证"
        ]),
        ("📈 阶段三：模型验证优化", "2个月", [
            "模型训练和调优",
            "临床验证测试",
            "用户体验评估",
            "安全性评估"
        ]),
        ("🚀 阶段四：应用推广", "持续", [
            "试点科室应用",
            "效果评估反馈",
            "持续数据更新",
            "功能迭代优化"
        ])
    ]

    for i, (phase_title, duration, tasks) in enumerate(phases_data):
        row = i // 2
        col = i % 2
        x_pos = Inches(0.5 + col * 6)
        y_pos = Inches(1.3 + row * 2.8)

        # 阶段标题
        phase_box = slide6.shapes.add_textbox(x_pos, y_pos, Inches(5.5), Inches(0.6))
        phase_frame = phase_box.text_frame
        phase_para = phase_frame.paragraphs[0]
        phase_para.text = f"{phase_title} ({duration})"
        phase_para.font.size = Pt(14)
        phase_para.font.bold = True
        phase_para.font.color.rgb = highlight_color

        # 任务列表
        tasks_box = slide6.shapes.add_textbox(x_pos, y_pos + Inches(0.6), Inches(5.5), Inches(2))
        tasks_frame = tasks_box.text_frame
        tasks_frame.margin_left = Inches(0.1)

        for task in tasks:
            p = tasks_frame.add_paragraph() if tasks_frame.paragraphs[0].text else tasks_frame.paragraphs[0]
            p.text = f"• {task}"
            p.font.size = Pt(11)
            p.font.color.rgb = text_color

    # 第7页：总结与下一步
    slide7 = prs.slides.add_slide(prs.slide_layouts[6])

    # 标题
    title_box = slide7.shapes.add_textbox(Inches(0.5), Inches(0.3), Inches(12.33), Inches(0.8))
    title_frame = title_box.text_frame
    title_frame.text = "📝 总结与下一步行动"
    title_para = title_frame.paragraphs[0]
    title_para.font.size = Pt(32)
    title_para.font.bold = True
    title_para.font.color.rgb = title_color

    # 现状总结
    summary_box = slide7.shapes.add_textbox(Inches(0.5), Inches(1.2), Inches(12), Inches(1.8))
    summary_frame = summary_box.text_frame
    summary_frame.margin_left = Inches(0.2)

    p1 = summary_frame.paragraphs[0]
    p1.text = "✅ 现状总结"
    p1.font.size = Pt(20)
    p1.font.bold = True
    p1.font.color.rgb = success_color

    summary_items = [
        "数据基础扎实：已建立10个功能模块，13,000+条数据",
        "技术框架完善：基于权威标准，AI增强生成",
        "应用场景明确：覆盖0-3岁全发育阶段",
        "关键缺口：真实临床数据、异常案例、专家验证"
    ]

    for item in summary_items:
        p = summary_frame.add_paragraph()
        p.text = f"• {item}"
        p.font.size = Pt(14)
        p.font.color.rgb = text_color
        p.level = 1

    # 核心合作目标
    goals_box = slide7.shapes.add_textbox(Inches(0.5), Inches(3.2), Inches(12), Inches(2))
    goals_frame = goals_box.text_frame
    goals_frame.margin_left = Inches(0.2)

    p1 = goals_frame.paragraphs[0]
    p1.text = "🎯 核心合作目标"
    p1.font.size = Pt(20)
    p1.font.bold = True
    p1.font.color.rgb = urgent_color

    goals_items = [
        "短期目标(3个月)：收集300+异常案例，完成1000条专家标注",
        "长期目标(6-12个月)：建立持续更新机制，完成临床应用验证",
        "即时行动：本周确定合作意向，下周制定详细方案"
    ]

    for item in goals_items:
        p = goals_frame.add_paragraph()
        p.text = f"• {item}"
        p.font.size = Pt(14)
        p.font.color.rgb = text_color
        p.level = 1

    # 结语
    conclusion_box = slide7.shapes.add_textbox(Inches(1), Inches(5.5), Inches(11.33), Inches(1.5))
    conclusion_frame = conclusion_box.text_frame
    conclusion_para = conclusion_frame.paragraphs[0]
    conclusion_para.text = "🚀 让我们携手推进儿童健康AI技术发展！"
    conclusion_para.font.size = Pt(24)
    conclusion_para.font.bold = True
    conclusion_para.font.color.rgb = highlight_color
    conclusion_para.alignment = PP_ALIGN.CENTER

    sub_conclusion = conclusion_frame.add_paragraph()
    sub_conclusion.text = "期待与医院专家团队的深度合作"
    sub_conclusion.font.size = Pt(18)
    sub_conclusion.font.color.rgb = text_color
    sub_conclusion.alignment = PP_ALIGN.CENTER

    return prs

def main():
    """主函数"""
    try:
        print("正在创建多模态育幼数据需求分析PPT...")
        prs = create_data_needs_presentation()
        
        # 保存文件
        output_file = "多模态育幼数据需求分析.pptx"
        prs.save(output_file)
        print(f"✅ PPT已成功创建：{output_file}")
        
        # 显示文件信息
        import os
        file_size = os.path.getsize(output_file) / 1024 / 1024
        print(f"📊 文件大小：{file_size:.2f} MB")
        print(f"📄 幻灯片数量：{len(prs.slides)} 页")
        
    except Exception as e:
        print(f"❌ 创建PPT时出错：{str(e)}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
