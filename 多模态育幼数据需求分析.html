<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>多模态育幼数据需求分析</title>
    <style>
        body {
            font-family: 'Microsoft YaHei', <PERSON>l, sans-serif;
            margin: 0;
            padding: 0;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: #333;
        }
        
        .presentation {
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
        }
        
        .slide {
            background: white;
            border-radius: 15px;
            padding: 40px;
            margin: 30px 0;
            box-shadow: 0 10px 30px rgba(0,0,0,0.2);
            min-height: 600px;
            display: none;
        }
        
        .slide.active {
            display: block;
        }
        
        h1 {
            color: #2c3e50;
            text-align: center;
            font-size: 2.5em;
            margin-bottom: 30px;
            border-bottom: 3px solid #3498db;
            padding-bottom: 15px;
        }
        
        h2 {
            color: #2980b9;
            font-size: 2em;
            margin-bottom: 25px;
            border-left: 5px solid #3498db;
            padding-left: 15px;
        }
        
        h3 {
            color: #34495e;
            font-size: 1.4em;
            margin: 20px 0 15px 0;
        }
        
        .overview-grid {
            display: grid;
            grid-template-columns: repeat(2, 1fr);
            gap: 20px;
            margin: 30px 0;
        }
        
        .overview-item {
            background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
            padding: 20px;
            border-radius: 10px;
            border-left: 4px solid #3498db;
        }
        
        .overview-item h4 {
            color: #2c3e50;
            margin: 0 0 10px 0;
            font-size: 1.2em;
        }
        
        .overview-item p {
            color: #7f8c8d;
            margin: 0;
            line-height: 1.6;
        }
        
        .status-card {
            background: linear-gradient(135deg, #e8f5e8 0%, #f0f8f0 100%);
            border: 2px solid #27ae60;
            border-radius: 10px;
            padding: 20px;
            margin: 15px 0;
        }
        
        .need-card {
            background: linear-gradient(135deg, #fff3cd 0%, #fef9e7 100%);
            border: 2px solid #f39c12;
            border-radius: 10px;
            padding: 20px;
            margin: 15px 0;
        }
        
        .urgent-card {
            background: linear-gradient(135deg, #f8d7da 0%, #fce4e6 100%);
            border: 2px solid #e74c3c;
            border-radius: 10px;
            padding: 20px;
            margin: 15px 0;
        }
        
        .stats-grid {
            display: grid;
            grid-template-columns: repeat(3, 1fr);
            gap: 20px;
            margin: 20px 0;
        }
        
        .stat-item {
            text-align: center;
            background: #f8f9fa;
            padding: 15px;
            border-radius: 8px;
        }
        
        .stat-number {
            font-size: 2em;
            font-weight: bold;
            color: #3498db;
        }
        
        .stat-label {
            color: #7f8c8d;
            font-size: 0.9em;
        }
        
        .highlight {
            background: linear-gradient(120deg, #a8e6cf 0%, #dcedc1 100%);
            padding: 2px 6px;
            border-radius: 4px;
            font-weight: bold;
        }
        
        .urgent {
            background: linear-gradient(120deg, #ffaaa5 0%, #ff8a80 100%);
            padding: 2px 6px;
            border-radius: 4px;
            font-weight: bold;
            color: white;
        }
        
        ul {
            line-height: 1.8;
        }
        
        li {
            margin: 8px 0;
        }
        
        .navigation {
            text-align: center;
            margin: 20px 0;
        }
        
        .nav-btn {
            background: #3498db;
            color: white;
            border: none;
            padding: 10px 20px;
            margin: 0 5px;
            border-radius: 5px;
            cursor: pointer;
            font-size: 1em;
        }
        
        .nav-btn:hover {
            background: #2980b9;
        }
        
        .nav-btn:disabled {
            background: #bdc3c7;
            cursor: not-allowed;
        }
        
        .collaboration-grid {
            display: grid;
            grid-template-columns: repeat(2, 1fr);
            gap: 25px;
            margin: 25px 0;
        }
        
        .collaboration-item {
            background: linear-gradient(135deg, #e3f2fd 0%, #f3e5f5 100%);
            padding: 20px;
            border-radius: 10px;
            border-left: 4px solid #9c27b0;
        }
    </style>
</head>
<body>
    <div class="presentation">
        <!-- 封面页 -->
        <div class="slide active">
            <h1>多模态育幼数据需求分析</h1>
            <div style="text-align: center; margin: 40px 0;">
                <div style="font-size: 1.5em; color: #7f8c8d; margin-bottom: 20px;">
                    基于现有10个功能模块的数据缺口分析
                </div>
                <div style="font-size: 1.2em; color: #95a5a6;">
                    医院合作·数据补充·临床验证
                </div>
            </div>
            
            <div class="stats-grid">
                <div class="stat-item">
                    <div class="stat-number">13,000+</div>
                    <div class="stat-label">现有数据条数</div>
                </div>
                <div class="stat-item">
                    <div class="stat-number">10</div>
                    <div class="stat-label">功能模块</div>
                </div>
                <div class="stat-item">
                    <div class="stat-number">50MB+</div>
                    <div class="stat-label">数据总量</div>
                </div>
            </div>
            
            <div class="overview-grid">
                <div class="overview-item">
                    <h4>✅ 已完成模块</h4>
                    <p>语音构音、运动监测、饮食指导、体格生长等10个核心功能</p>
                </div>
                <div class="overview-item">
                    <h4>🔍 数据缺口</h4>
                    <p>临床真实数据、异常案例、多模态融合验证</p>
                </div>
                <div class="overview-item">
                    <h4>🏥 医院合作</h4>
                    <p>临床数据采集、专家标注、验证测试</p>
                </div>
                <div class="overview-item">
                    <h4>🎯 目标提升</h4>
                    <p>数据质量、临床适用性、模型准确性</p>
                </div>
            </div>
        </div>

        <!-- 第2页：现有数据集概览 -->
        <div class="slide">
            <h2>📊 现有数据集完成情况</h2>
            
            <div class="status-card">
                <h3>✅ 已完成的10个功能模块</h3>
                <div style="display: grid; grid-template-columns: repeat(2, 1fr); gap: 15px;">
                    <div>
                        <strong>🗣️ 语音构音监测</strong> - 1,219条<br>
                        <small>基于52词汇标准化测试</small>
                    </div>
                    <div>
                        <strong>🏃 家庭场景运动监测</strong> - 1,499条<br>
                        <small>CribHD多模态数据集</small>
                    </div>
                    <div>
                        <strong>🍎 饮食多样性监测</strong> - 571条<br>
                        <small>基于国家卫健委指南</small>
                    </div>
                    <div>
                        <strong>📏 体格生长监测</strong> - 400条<br>
                        <small>多指标综合分析</small>
                    </div>
                    <div>
                        <strong>🎯 发音纠正指导</strong> - 650+条<br>
                        <small>五步纠正法</small>
                    </div>
                    <div>
                        <strong>🤸 粗大运动发展</strong> - 4,586条<br>
                        <small>0-36个月里程碑</small>
                    </div>
                    <div>
                        <strong>✋ 精细动作发展</strong> - 827条<br>
                        <small>四维度结构化分析</small>
                    </div>
                    <div>
                        <strong>😊 社会情绪发展</strong> - 4,041条<br>
                        <small>PEC理论+ASQ:SE-2</small>
                    </div>
                    <div>
                        <strong>💬 语言发展指导</strong> - 707条<br>
                        <small>基于专业教材</small>
                    </div>
                    <div>
                        <strong>🥄 膳食营养指导</strong><br>
                        <small>已整合到饮食监测中</small>
                    </div>
                </div>
            </div>
            
            <h3>📈 数据来源分析</h3>
            <ul>
                <li><span class="highlight">权威标准</span>：国家卫健委、WHO、专业教材</li>
                <li><span class="highlight">AI生成</span>：Qwen系列模型增强生成</li>
                <li><span class="highlight">专业验证</span>：基于循证医学原则</li>
                <li><span class="urgent">缺少</span>：真实临床数据、异常案例</li>
            </ul>
        </div>

        <!-- 第3页：关键数据缺口分析 -->
        <div class="slide">
            <h2>🔍 关键数据缺口分析</h2>

            <div class="urgent-card">
                <h3>🚨 急需补充的数据类型</h3>
                <ul>
                    <li><span class="urgent">真实临床数据</span>：目前主要依赖AI生成，缺乏真实病例</li>
                    <li><span class="urgent">异常发育案例</span>：正常发育数据充足，异常案例稀缺</li>
                    <li><span class="urgent">多模态融合验证</span>：各模块独立，缺乏跨模态关联</li>
                    <li><span class="urgent">长期追踪数据</span>：缺乏同一婴儿的发育轨迹数据</li>
                </ul>
            </div>

            <div class="need-card">
                <h3>⚠️ 需要改进的数据质量</h3>
                <ul>
                    <li><span class="highlight">专家标注验证</span>：AI生成数据需要医学专家审核</li>
                    <li><span class="highlight">数据平衡性</span>：某些年龄段、性别数据分布不均</li>
                    <li><span class="highlight">地域代表性</span>：主要基于中文环境，缺乏多地域验证</li>
                    <li><span class="highlight">实时性验证</span>：静态数据较多，缺乏动态监测验证</li>
                </ul>
            </div>

            <h3>📊 各模块数据缺口详情</h3>
            <div style="display: grid; grid-template-columns: repeat(2, 1fr); gap: 20px;">
                <div>
                    <strong>语音构音监测</strong>
                    <ul style="font-size: 0.9em;">
                        <li>缺少方言影响数据</li>
                        <li>缺少听力障碍案例</li>
                        <li>缺少治疗效果追踪</li>
                    </ul>
                </div>
                <div>
                    <strong>运动发展监测</strong>
                    <ul style="font-size: 0.9em;">
                        <li>缺少运动障碍案例</li>
                        <li>缺少环境因素影响</li>
                        <li>缺少康复训练数据</li>
                    </ul>
                </div>
                <div>
                    <strong>营养发育监测</strong>
                    <ul style="font-size: 0.9em;">
                        <li>缺少营养不良案例</li>
                        <li>缺少过敏反应数据</li>
                        <li>缺少文化饮食差异</li>
                    </ul>
                </div>
                <div>
                    <strong>情绪社交发展</strong>
                    <ul style="font-size: 0.9em;">
                        <li>缺少自闭症谱系数据</li>
                        <li>缺少家庭环境影响</li>
                        <li>缺少干预效果评估</li>
                    </ul>
                </div>
            </div>
        </div>

        <!-- 第4页：医院合作需求 -->
        <div class="slide">
            <h2>🏥 医院合作数据需求</h2>

            <div class="collaboration-grid">
                <div class="collaboration-item">
                    <h4>🎯 临床数据采集</h4>
                    <ul>
                        <li>真实患儿发育评估记录</li>
                        <li>医生诊断和治疗方案</li>
                        <li>家长反馈和观察记录</li>
                        <li>治疗效果追踪数据</li>
                    </ul>
                </div>

                <div class="collaboration-item">
                    <h4>👨‍⚕️ 专家标注服务</h4>
                    <ul>
                        <li>儿科医生专业标注</li>
                        <li>发育评估专家审核</li>
                        <li>语言治疗师验证</li>
                        <li>营养师专业指导</li>
                    </ul>
                </div>

                <div class="collaboration-item">
                    <h4>🔬 异常案例收集</h4>
                    <ul>
                        <li>发育迟缓案例</li>
                        <li>语言障碍案例</li>
                        <li>运动发育异常</li>
                        <li>营养相关疾病</li>
                    </ul>
                </div>

                <div class="collaboration-item">
                    <h4>📈 验证测试支持</h4>
                    <ul>
                        <li>模型准确性验证</li>
                        <li>临床适用性测试</li>
                        <li>用户体验评估</li>
                        <li>安全性评估</li>
                    </ul>
                </div>
            </div>

            <div class="status-card">
                <h3>🎯 预期合作成果</h3>
                <div style="display: grid; grid-template-columns: repeat(3, 1fr); gap: 15px;">
                    <div>
                        <strong>数据质量提升</strong><br>
                        <small>真实性、准确性、完整性</small>
                    </div>
                    <div>
                        <strong>临床适用性</strong><br>
                        <small>实际应用场景验证</small>
                    </div>
                    <div>
                        <strong>专业认可度</strong><br>
                        <small>医学界权威认证</small>
                    </div>
                </div>
            </div>
        </div>

        <!-- 第5页：具体数据需求清单 -->
        <div class="slide">
            <h2>📋 具体数据需求清单</h2>

            <div class="urgent-card">
                <h3>🔥 高优先级数据需求</h3>
                <div style="display: grid; grid-template-columns: repeat(2, 1fr); gap: 20px;">
                    <div>
                        <h4>1. 异常发育案例 (急需)</h4>
                        <ul>
                            <li>发育迟缓：100-200例</li>
                            <li>语言障碍：50-100例</li>
                            <li>运动发育异常：50-100例</li>
                            <li>社交情绪问题：30-50例</li>
                        </ul>
                    </div>
                    <div>
                        <h4>2. 专家标注数据 (急需)</h4>
                        <ul>
                            <li>现有AI数据专家审核：1000条</li>
                            <li>新增专家标注案例：500条</li>
                            <li>诊断标准对照：200条</li>
                            <li>治疗方案验证：100条</li>
                        </ul>
                    </div>
                </div>
            </div>

            <div class="need-card">
                <h3>⭐ 中等优先级数据需求</h3>
                <div style="display: grid; grid-template-columns: repeat(2, 1fr); gap: 20px;">
                    <div>
                        <h4>3. 长期追踪数据</h4>
                        <ul>
                            <li>同一婴儿6个月追踪：50例</li>
                            <li>治疗效果追踪：30例</li>
                            <li>发育轨迹记录：100例</li>
                            <li>家庭环境影响：50例</li>
                        </ul>
                    </div>
                    <div>
                        <h4>4. 多模态融合数据</h4>
                        <ul>
                            <li>视频+音频+文本：100例</li>
                            <li>生理指标+行为观察：50例</li>
                            <li>环境因素+发育评估：80例</li>
                            <li>家长反馈+专业评估：100例</li>
                        </ul>
                    </div>
                </div>
            </div>

            <div class="status-card">
                <h3>📊 数据收集时间规划</h3>
                <ul>
                    <li><span class="highlight">第1个月</span>：建立合作框架，开始异常案例收集</li>
                    <li><span class="highlight">第2-3个月</span>：专家标注，数据质量验证</li>
                    <li><span class="highlight">第4-6个月</span>：长期追踪数据收集</li>
                    <li><span class="highlight">第6个月后</span>：持续数据更新和模型优化</li>
                </ul>
            </div>
        </div>

        <!-- 第6页：合作方案建议 -->
        <div class="slide">
            <h2>🤝 合作方案建议</h2>

            <div class="collaboration-grid">
                <div class="collaboration-item">
                    <h4>📋 阶段一：数据收集框架建立</h4>
                    <ul>
                        <li><strong>时间</strong>：1个月</li>
                        <li><strong>目标</strong>：建立数据收集标准</li>
                        <li><strong>内容</strong>：
                            <ul>
                                <li>制定数据收集协议</li>
                                <li>建立伦理审查流程</li>
                                <li>培训数据收集人员</li>
                                <li>搭建数据管理平台</li>
                            </ul>
                        </li>
                    </ul>
                </div>

                <div class="collaboration-item">
                    <h4>🔬 阶段二：重点数据采集</h4>
                    <ul>
                        <li><strong>时间</strong>：3个月</li>
                        <li><strong>目标</strong>：收集高质量临床数据</li>
                        <li><strong>内容</strong>：
                            <ul>
                                <li>异常发育案例收集</li>
                                <li>专家标注和审核</li>
                                <li>多模态数据采集</li>
                                <li>质量控制和验证</li>
                            </ul>
                        </li>
                    </ul>
                </div>

                <div class="collaboration-item">
                    <h4>📈 阶段三：模型验证优化</h4>
                    <ul>
                        <li><strong>时间</strong>：2个月</li>
                        <li><strong>目标</strong>：提升模型性能</li>
                        <li><strong>内容</strong>：
                            <ul>
                                <li>模型训练和调优</li>
                                <li>临床验证测试</li>
                                <li>用户体验评估</li>
                                <li>安全性评估</li>
                            </ul>
                        </li>
                    </ul>
                </div>

                <div class="collaboration-item">
                    <h4>🚀 阶段四：应用推广</h4>
                    <ul>
                        <li><strong>时间</strong>：持续</li>
                        <li><strong>目标</strong>：临床应用推广</li>
                        <li><strong>内容</strong>：
                            <ul>
                                <li>试点科室应用</li>
                                <li>效果评估反馈</li>
                                <li>持续数据更新</li>
                                <li>功能迭代优化</li>
                            </ul>
                        </li>
                    </ul>
                </div>
            </div>

            <div class="status-card">
                <h3>💰 预期投入与回报</h3>
                <div style="display: grid; grid-template-columns: repeat(2, 1fr); gap: 20px;">
                    <div>
                        <h4>医院投入</h4>
                        <ul>
                            <li>专家时间：每周10-15小时</li>
                            <li>数据收集：专门人员配置</li>
                            <li>设备支持：数据采集设备</li>
                            <li>伦理审查：IRB审批流程</li>
                        </ul>
                    </div>
                    <div>
                        <h4>预期回报</h4>
                        <ul>
                            <li>提升诊断效率和准确性</li>
                            <li>获得先进AI辅助工具</li>
                            <li>积累宝贵科研数据</li>
                            <li>提升医院技术影响力</li>
                        </ul>
                    </div>
                </div>
            </div>
        </div>

        <!-- 第7页：总结与下一步 -->
        <div class="slide">
            <h2>📝 总结与下一步行动</h2>

            <div class="status-card">
                <h3>✅ 现状总结</h3>
                <ul>
                    <li><span class="highlight">数据基础扎实</span>：已建立10个功能模块，13,000+条数据</li>
                    <li><span class="highlight">技术框架完善</span>：基于权威标准，AI增强生成</li>
                    <li><span class="highlight">应用场景明确</span>：覆盖0-3岁全发育阶段</li>
                    <li><span class="urgent">关键缺口</span>：真实临床数据、异常案例、专家验证</li>
                </ul>
            </div>

            <div class="urgent-card">
                <h3>🎯 核心合作目标</h3>
                <div style="display: grid; grid-template-columns: repeat(2, 1fr); gap: 20px;">
                    <div>
                        <h4>短期目标 (3个月)</h4>
                        <ul>
                            <li>收集300+异常发育案例</li>
                            <li>完成1000条专家标注</li>
                            <li>建立数据质量标准</li>
                            <li>启动临床验证测试</li>
                        </ul>
                    </div>
                    <div>
                        <h4>长期目标 (6-12个月)</h4>
                        <ul>
                            <li>建立持续数据更新机制</li>
                            <li>完成临床应用验证</li>
                            <li>推广到多个科室使用</li>
                            <li>形成标准化应用流程</li>
                        </ul>
                    </div>
                </div>
            </div>

            <div class="need-card">
                <h3>📞 即时行动计划</h3>
                <ol style="font-size: 1.1em; line-height: 1.8;">
                    <li><strong>本周</strong>：确定合作意向，签署初步协议</li>
                    <li><strong>下周</strong>：制定详细数据收集方案</li>
                    <li><strong>本月内</strong>：完成伦理审查申请</li>
                    <li><strong>下月开始</strong>：正式启动数据收集工作</li>
                </ol>
            </div>

            <div style="text-align: center; margin: 30px 0; padding: 20px; background: linear-gradient(135deg, #74b9ff 0%, #0984e3 100%); border-radius: 10px; color: white;">
                <h3 style="color: white; margin: 0 0 15px 0;">🚀 让我们携手推进儿童健康AI技术发展！</h3>
                <p style="margin: 0; font-size: 1.1em;">期待与医院专家团队的深度合作</p>
            </div>
        </div>
    </div>

    <!-- 导航控制 -->
    <div class="navigation">
        <button class="nav-btn" onclick="previousSlide()">← 上一页</button>
        <span id="slideInfo">1 / 7</span>
        <button class="nav-btn" onclick="nextSlide()">下一页 →</button>
    </div>

    <script>
        let currentSlide = 0;
        const slides = document.querySelectorAll('.slide');
        const totalSlides = slides.length;

        function showSlide(n) {
            slides[currentSlide].classList.remove('active');
            currentSlide = (n + totalSlides) % totalSlides;
            slides[currentSlide].classList.add('active');
            document.getElementById('slideInfo').textContent = `${currentSlide + 1} / ${totalSlides}`;

            // 更新导航按钮状态
            document.querySelector('.nav-btn').disabled = currentSlide === 0;
            document.querySelectorAll('.nav-btn')[1].disabled = currentSlide === totalSlides - 1;
        }

        function nextSlide() {
            if (currentSlide < totalSlides - 1) {
                showSlide(currentSlide + 1);
            }
        }

        function previousSlide() {
            if (currentSlide > 0) {
                showSlide(currentSlide - 1);
            }
        }

        // 键盘导航
        document.addEventListener('keydown', function(e) {
            if (e.key === 'ArrowRight' || e.key === ' ') {
                nextSlide();
            } else if (e.key === 'ArrowLeft') {
                previousSlide();
            }
        });

        // 初始化
        showSlide(0);
    </script>
</body>
</html>
